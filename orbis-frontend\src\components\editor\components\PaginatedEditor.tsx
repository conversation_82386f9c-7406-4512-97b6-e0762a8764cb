'use client'

import React, { useEffect } from 'react'
import { EditorContent } from '@tiptap/react'
import { usePagination } from '../hooks/usePagination'
import { Editor } from '@tiptap/react'
import '../styles/pagination.css'

interface PaginatedEditorProps {
  editor: Editor | null
  documentTitle?: string
  className?: string
}

export default function PaginatedEditor({
  editor,
  documentTitle = 'Document CCTP',
  className = ''
}: PaginatedEditorProps) {
  const {
    pages,
    currentPage,
    containerRef,
    generateFooter,
    insertPageBreak,
    updatePagination
  } = usePagination({
    editor,
    documentTitle
  })

  // Fonction pour faire défiler vers une page spécifique
  const scrollToPage = (pageNumber: number) => {
    if (!containerRef.current) return

    const pageHeight = 1123 + 20 // Hauteur page + marge
    const targetY = (pageNumber - 1) * pageHeight

    containerRef.current.scrollTo({
      top: targetY,
      behavior: 'smooth'
    })
  }

  // Générer les pages avec pieds de page
  const renderPages = () => {
    return pages.map((pageNumber) => (
      <div key={pageNumber} className="document-page">
        <div className="page-content">
          {pageNumber === 1 && (
            <EditorContent editor={editor} />
          )}
        </div>
        <div 
          className="page-footer"
          dangerouslySetInnerHTML={{ __html: generateFooter(pageNumber) }}
        />
      </div>
    ))
  }

  if (!editor) {
    return (
      <div className="paginated-editor">
        <div className="loading-editor">
          <p>Chargement de l'éditeur...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`paginated-editor ${className}`}>
      {/* Barre d'outils de pagination */}
      <div className="pagination-toolbar">
        <button
          onClick={insertPageBreak}
          className="page-break-button"
          title="Insérer un saut de page (Ctrl+Entrée)"
        >
          📄 Saut de page
        </button>
        
        <div className="page-navigation">
          <button
            onClick={() => scrollToPage(Math.max(1, currentPage - 1))}
            disabled={currentPage <= 1}
            className="nav-button"
          >
            ← Page précédente
          </button>
          
          <span className="page-info">
            Page {currentPage} sur {pages.length}
          </span>
          
          <button
            onClick={() => scrollToPage(Math.min(pages.length, currentPage + 1))}
            disabled={currentPage >= pages.length}
            className="nav-button"
          >
            Page suivante →
          </button>
        </div>
      </div>

      {/* Zone de contenu paginé */}
      <div 
        ref={containerRef}
        className="paginated-content"
      >
        {/* Version simplifiée : une seule page avec l'éditeur */}
        <div className="document-page">
          <div className="page-content">
            <EditorContent editor={editor} />
          </div>
          <div 
            className="page-footer"
            dangerouslySetInnerHTML={{ __html: generateFooter(1) }}
          />
        </div>
      </div>

      {/* Indicateur de page courante */}
      <div className="page-indicator">
        Page {currentPage} / {pages.length}
      </div>
    </div>
  )
}
