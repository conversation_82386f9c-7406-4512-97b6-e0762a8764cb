'use client'

import React, { useState, useEffect } from 'react'
import { EditorContent } from '@tiptap/react'
import { useEditorSetup } from './hooks/useEditorSetup'
import { useEditorActions } from './hooks/useEditorActions'
import { useCoverPageGeneration } from './hooks/useCoverPageGeneration'
import { usePagination } from './hooks/usePagination'
import EditorToolbar from './components/EditorToolbar'
import EditorSidebar from './components/EditorSidebar'
import PaginationToolbar from './components/PaginationToolbar'
import AddArticleModal from './AddArticleModal'
import { DocumentType } from '@/types/technical-document'
import './styles/modern-editor.css'
import './styles/pagination.css'

interface PaginatedDocumentEditorProps {
  value: string
  onChange: (content: string) => void
  documentType: DocumentType
  onTextSelection?: (selectedText: string) => void
  readOnly?: boolean
  workspaceName?: string
  workspaceLogo?: string
  onAddArticle?: (articleData: any) => void
  onSave?: () => Promise<void>
  onClose?: () => void
  hasUnsavedChanges?: boolean
  lotId?: number | null
  documentIndice?: string
  documentTitle?: string
}

export default function PaginatedDocumentEditor({
  value,
  onChange,
  documentType,
  onTextSelection,
  readOnly = false,
  workspaceName = '',
  workspaceLogo = '',
  onAddArticle,
  onSave,
  onClose,
  hasUnsavedChanges = false,
  lotId,
  documentIndice = "01",
  documentTitle = 'Document CCTP'
}: PaginatedDocumentEditorProps) {
  
  // Configuration de l'éditeur
  const { editor } = useEditorSetup({
    value,
    onChange,
    onTextSelection,
    readOnly
  })

  // Actions de l'éditeur
  const {
    isSaving,
    isGeneratingArticle,
    showCloseConfirm,
    showImportModal,
    showAddArticleModal,
    availableHeadings,
    setShowImportModal,
    setShowAddArticleModal,
    insertNumberedHeading,
    initializeCCTPStructure,
    handleSave,
    handleClose,
    confirmClose,
    cancelClose,
    handleFileImport,
    handleAddArticle,
    handleAddArticleJSON,
    fileInputRef
  } = useEditorActions({
    editor,
    onSave,
    onClose,
    hasUnsavedChanges
  })

  // Génération de page de garde
  const { generateCoverPage, isGenerating: isGeneratingCoverPage } = useCoverPageGeneration({
    editor,
    lotId,
    documentType: documentType.toString(),
    documentIndice
  })

  // Pagination
  const {
    pages,
    currentPage,
    setCurrentPage,
    containerRef,
    generateFooter,
    insertPageBreak,
    updatePagination
  } = usePagination({
    editor,
    documentTitle
  })

  if (!editor) {
    return (
      <div className="google-docs-editor">
        <div className="loading-editor">
          <p>Chargement de l'éditeur...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="google-docs-editor">
      {/* Zone principale d'édition */}
      <div className="editor-main-area">
        {/* Sidebar simple à gauche */}
        <EditorSidebar
          lotId={lotId}
          isSaving={isSaving}
          hasUnsavedChanges={hasUnsavedChanges}
          onSave={handleSave}
          onClose={handleClose}
          onGenerateCoverPage={generateCoverPage}
          onShowAddArticleModal={() => setShowAddArticleModal(true)}
          isGeneratingCoverPage={isGeneratingCoverPage}
          isGeneratingArticle={isGeneratingArticle}
        />

        {/* Zone d'édition principale */}
        <div className="editor-main">
          {/* Barre d'outils principale */}
          <EditorToolbar
            editor={editor}
            onInsertHeading={insertNumberedHeading}
            onShowImportModal={() => setShowImportModal(true)}
          />

          {/* Barre d'outils de pagination */}
          <PaginationToolbar
            editor={editor}
            currentPage={currentPage}
            totalPages={pages.length}
            onPageChange={setCurrentPage}
          />

          {/* Zone d'édition avec pagination */}
          <div className="paginated-editor-container">
            <div
              ref={containerRef}
              className="paginated-content"
            >
              {/* Vérifier s'il y a une page de garde */}
              {editor && (() => {
                const doc = editor.state.doc
                let hasCoverPage = false

                doc.descendants((node) => {
                  if (node.type.name === 'coverPage') {
                    hasCoverPage = true
                    return false
                  }
                })

                return (
                  <>
                    {/* Page principale avec contenu (incluant la page de garde si présente) */}
                    <div className="document-page">
                      <div className="page-content">
                        <EditorContent editor={editor} />
                      </div>
                      {/* Pied de page seulement si pas de page de garde */}
                      {!hasCoverPage && (
                        <div
                          className="page-footer"
                          dangerouslySetInnerHTML={{ __html: generateFooter(currentPage, false, false) }}
                        />
                      )}
                    </div>
                  </>
                )
              })()}
            </div>

            {/* Indicateur de page courante */}
            <div className="page-indicator">
              Page {currentPage} / {pages.length}
            </div>
          </div>
        </div>
      </div>

      {/* Modales */}
      {showAddArticleModal && (
        <AddArticleModal
          isOpen={showAddArticleModal}
          onClose={() => setShowAddArticleModal(false)}
          onSubmit={handleAddArticleJSON}
          availableHeadings={availableHeadings}
          isGenerating={isGeneratingArticle}
        />
      )}

      {/* Modal de confirmation de fermeture */}
      {showCloseConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Modifications non sauvegardées</h3>
            <p className="text-gray-600 mb-6">
              Vous avez des modifications non sauvegardées. Voulez-vous les sauvegarder avant de fermer ?
            </p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={cancelClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={confirmClose}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Fermer sans sauvegarder
              </button>
              <button
                onClick={async () => {
                  await handleSave()
                  confirmClose()
                }}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Sauvegarder et fermer
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Input caché pour l'import de fichiers */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".docx,.doc"
        style={{ display: 'none' }}
        onChange={handleFileImport}
      />
    </div>
  )
}
